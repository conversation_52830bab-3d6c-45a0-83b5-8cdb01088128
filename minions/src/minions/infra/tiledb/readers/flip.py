from minions.infra.tiledb.axes import Axes
from minions.infra.tiledb.readers.base import <PERSON><PERSON><PERSON><PERSON>, ComposeReader, ImageReader

SUPPORTED_FLIP_AXES = [Axes.X, Axes.Y]


class FlipReader(ComposeReader):
    def __init__(self, src_reader: <PERSON>Reader, flip_axes: Axes):
        super().__init__(src_reader)
        self.flip_axes = flip_axes

    def level_image(self, level: int, slices: tuple[slice, ...] | None = None):
        level_shape = self.src_reader.level_shape(level)
        if slices is None:
            # If no slices provided, create full slices for all axes
            slices = tuple(slice(None) for _ in level_shape.axes.dims)

        # Transform slices to account for flipping
        flipped_slices = self._flip_slices(level_shape, slices)

        # Get the data with flipped slices
        flipped_arr = self.src_reader.level_image(level, flipped_slices)
        return flipped_arr

    def _flip_slices(self, level_shape: AxesData[int], slices: tuple[slice, ...]) -> tuple[slice, ...]:
        """Transform slices to account for coordinate flipping."""
        # Convert slices tuple to list for modification
        flipped_slices = list(slices)
        src_axes = level_shape.axes

        # Flip slices for each axis that should be flipped
        for flip_axis in self.flip_axes.dims:
            if flip_axis in src_axes.dims:
                shape_in_axis = level_shape.raw[flip_axis]
                axis_idx = src_axes.dims.index(flip_axis)

                original_slice = flipped_slices[axis_idx]
                flipped_slices[axis_idx] = flip_slice_with_size(original_slice, shape_in_axis)

        return tuple(flipped_slices)


def flip_slice_with_size(original_slice: slice, size_in_axis: int) -> slice:
    """Flip a single slice coordinate for a given axis size.

    for a arr with slice s, np.flip(arr)[s] == arr[flipped_s].

    Args:
        original_slice: The original slice object
        size_in_axis: The size of the array along this axis

    Returns:
        A new slice that when applied to the original array gives the same result
        as applying the original slice to the flipped array.

    Examples:
        >>> # For array of size 10
        >>> flip_slice_with_size(slice(2, 5), 10)
        slice(5, 8, None)
        >>> flip_slice_with_size(slice(None, None, 2), 10)
        slice(None, None, -2)

    """
    start, stop, step = original_slice.indices(size_in_axis)

    # When we flip an array, index i becomes index (size-1-i)
    # For slice(start, stop, step) on flipped array to equal arr[flipped_slice]:
    # - Each index i in range(start, stop, step) maps to (size-1-i) in original array
    # - We need to collect these mapped indices in the same order

    if step > 0:
        # Forward slice: start < stop
        # Indices accessed: start, start+step, start+2*step, ..., up to stop-1
        # These map to: size-1-start, size-1-(start+step), ..., size-1-(stop-1)
        # Which is: size-1-start, size-1-start-step, ..., size-stop
        # To get these in order, we need to go backwards from size-1-start to size-stop
        new_start = size_in_axis - 1 - start
        new_stop = size_in_axis - stop - 1  # -1 because stop is exclusive
        new_step = -step
    else:
        # Backward slice: start > stop, step < 0
        # This is more complex - let's handle it step by step
        # Indices accessed: start, start+step, start+2*step, ..., down to stop+1
        new_start = size_in_axis - 1 - start
        new_stop = size_in_axis - 1 - stop
        new_step = -step

    # Handle None values properly for slice boundaries
    if new_stop < 0:
        new_stop = None
    if new_start >= size_in_axis:
        new_start = None

    return slice(new_start, new_stop, new_step)
