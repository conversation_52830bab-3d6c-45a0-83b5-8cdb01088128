import numpy as np
import pytest

from minions.infra.tiledb.readers.flip import flip_slice_with_size


class TestFlipSliceWithSize:
    """Test cases for flip_slice_with_size function."""

    def test_basic_forward_slice(self):
        """Test basic forward slice like slice(2, 5) with size 10."""
        # For array of size 10, slice(2, 5) should become slice(7, 4, -1)
        result = flip_slice_with_size(slice(2, 5), 10)
        expected = slice(7, 4, -1)
        assert result == expected

    def test_full_slice(self):
        """Test full slice like slice(None) with various sizes."""
        # Verify using property-based approach
        size = 10
        original_slice = slice(None)
        result = flip_slice_with_size(original_slice, size)

        # Verify the property holds
        arr = np.arange(size)
        flipped_arr = np.flip(arr)
        np.testing.assert_array_equal(flipped_arr[original_slice], arr[result])

    def test_slice_with_step(self):
        """Test slice with positive step."""
        # Verify using property-based approach
        size = 10
        original_slice = slice(0, 10, 2)
        result = flip_slice_with_size(original_slice, size)

        # Verify the property holds
        arr = np.arange(size)
        flipped_arr = np.flip(arr)
        np.testing.assert_array_equal(flipped_arr[original_slice], arr[result])

    def test_slice_with_negative_step(self):
        """Test slice with negative step."""
        # Verify using property-based approach
        size = 10
        original_slice = slice(9, -1, -2)
        result = flip_slice_with_size(original_slice, size)

        # Verify the property holds
        arr = np.arange(size)
        flipped_arr = np.flip(arr)
        np.testing.assert_array_equal(flipped_arr[original_slice], arr[result])

    def test_slice_from_start(self):
        """Test slice starting from beginning."""
        # Verify using property-based approach
        size = 10
        original_slice = slice(0, 5)
        result = flip_slice_with_size(original_slice, size)

        # Verify the property holds
        arr = np.arange(size)
        flipped_arr = np.flip(arr)
        np.testing.assert_array_equal(flipped_arr[original_slice], arr[result])

    def test_slice_to_end(self):
        """Test slice going to the end."""
        # Verify using property-based approach
        size = 10
        original_slice = slice(5, None)
        result = flip_slice_with_size(original_slice, size)

        # Verify the property holds
        arr = np.arange(size)
        flipped_arr = np.flip(arr)
        np.testing.assert_array_equal(flipped_arr[original_slice], arr[result])

    def test_slice_with_none_start(self):
        """Test slice with None start."""
        # Verify using property-based approach
        size = 10
        original_slice = slice(None, 5)
        result = flip_slice_with_size(original_slice, size)

        # Verify the property holds
        arr = np.arange(size)
        flipped_arr = np.flip(arr)
        np.testing.assert_array_equal(flipped_arr[original_slice], arr[result])

    def test_single_element_slice(self):
        """Test slice that selects single element."""
        # Verify using property-based approach
        size = 10
        original_slice = slice(3, 4)
        result = flip_slice_with_size(original_slice, size)

        # Verify the property holds
        arr = np.arange(size)
        flipped_arr = np.flip(arr)
        np.testing.assert_array_equal(flipped_arr[original_slice], arr[result])

    def test_empty_slice(self):
        """Test slice that selects no elements."""
        # Verify using property-based approach
        size = 10
        original_slice = slice(5, 5)
        result = flip_slice_with_size(original_slice, size)

        # Verify the property holds
        arr = np.arange(size)
        flipped_arr = np.flip(arr)
        np.testing.assert_array_equal(flipped_arr[original_slice], arr[result])

    def test_backward_slice(self):
        """Test backward slice with negative step."""
        # Verify using property-based approach
        size = 10
        original_slice = slice(8, 2, -2)
        result = flip_slice_with_size(original_slice, size)

        # Verify the property holds
        arr = np.arange(size)
        flipped_arr = np.flip(arr)
        np.testing.assert_array_equal(flipped_arr[original_slice], arr[result])

    def test_property_based_verification(self):
        """Test that the flip property holds: np.flip(arr)[s] == arr[flipped_s]."""
        # Test with various array sizes and slice configurations
        test_cases = [
            (10, slice(2, 8)),
            (10, slice(0, 10, 2)),
            (10, slice(9, -1, -1)),
            (20, slice(5, 15)),
            (20, slice(None, None, 3)),
            (15, slice(10, 2, -2)),
        ]

        for size, original_slice in test_cases:
            # Create test array
            arr = np.arange(size)

            # Get flipped slice
            flipped_slice = flip_slice_with_size(original_slice, size)

            # Verify the property: np.flip(arr)[original_slice] == arr[flipped_slice]
            flipped_arr = np.flip(arr)

            # Handle empty slices
            original_result = flipped_arr[original_slice]
            flipped_result = arr[flipped_slice]

            np.testing.assert_array_equal(
                original_result,
                flipped_result,
                err_msg=f"Failed for size={size}, slice={original_slice}, flipped_slice={flipped_slice}",
            )

    def test_edge_cases(self):
        """Test edge cases with boundary conditions."""
        # Test with size 1 - verify using property-based approach
        size = 1
        original_slice = slice(None)
        result = flip_slice_with_size(original_slice, size)

        arr = np.arange(size)
        flipped_arr = np.flip(arr)
        np.testing.assert_array_equal(flipped_arr[original_slice], arr[result])

        # Test with size 2 - verify using property-based approach
        size = 2
        original_slice = slice(0, 1)
        result = flip_slice_with_size(original_slice, size)

        arr = np.arange(size)
        flipped_arr = np.flip(arr)
        np.testing.assert_array_equal(flipped_arr[original_slice], arr[result])

    def test_large_step_sizes(self):
        """Test with large step sizes."""
        # Verify using property-based approach
        size = 10
        original_slice = slice(0, 10, 5)
        result = flip_slice_with_size(original_slice, size)

        arr = np.arange(size)
        flipped_arr = np.flip(arr)
        np.testing.assert_array_equal(flipped_arr[original_slice], arr[result])

    def test_docstring_examples(self):
        """Test the examples from the function's docstring."""
        # Example 1: slice(2, 5) with size 10 - verify using property-based approach
        size = 10
        original_slice = slice(2, 5)
        result = flip_slice_with_size(original_slice, size)

        arr = np.arange(size)
        flipped_arr = np.flip(arr)
        np.testing.assert_array_equal(flipped_arr[original_slice], arr[result])

        # Example 2: slice(None, None, 2) with size 10 - verify using property-based approach
        size = 10
        original_slice = slice(None, None, 2)
        result = flip_slice_with_size(original_slice, size)

        arr = np.arange(size)
        flipped_arr = np.flip(arr)
        np.testing.assert_array_equal(flipped_arr[original_slice], arr[result])

    @pytest.mark.parametrize("size", [1, 2, 5, 10, 100])
    @pytest.mark.parametrize(
        "start,stop,step",
        [
            (0, None, 1),
            (None, None, 1),
            (1, 5, 1),
            (0, 10, 2),
            (9, -1, -1),
            (5, 0, -1),
        ],
    )
    def test_parametrized_slices(self, size, start, stop, step):
        """Parametrized test for various slice configurations."""
        if stop is not None and stop > size:
            stop = size
        if start is not None and start >= size:
            start = size - 1

        original_slice = slice(start, stop, step)

        # Skip invalid slices
        try:
            test_indices = list(range(*original_slice.indices(size)))
            if not test_indices:
                return  # Skip empty slices for this test
        except ValueError:
            return  # Skip invalid slices

        flipped_slice = flip_slice_with_size(original_slice, size)

        # Verify with actual arrays
        arr = np.arange(size)
        flipped_arr = np.flip(arr)

        original_result = flipped_arr[original_slice]
        flipped_result = arr[flipped_slice]

        np.testing.assert_array_equal(original_result, flipped_result)
